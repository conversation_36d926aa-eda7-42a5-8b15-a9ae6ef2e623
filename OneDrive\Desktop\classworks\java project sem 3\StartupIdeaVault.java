import java.io.*;
import java.util.*;

class StartupIdea {
    int id;
    String title, problem, solution, category, targetUsers;

    public StartupIdea(int id, String title, String problem, String solution, String category, String targetUsers) {
        this.id = id;
        this.title = title;
        this.problem = problem;
        this.solution = solution;
        this.category = category;
        this.targetUsers = targetUsers;
    }

    public String toFileString() {
        return id + "|" + title + "|" + problem + "|" + solution + "|" + category + "|" + targetUsers;
    }

    public static StartupIdea fromFileString(String line) {
        String[] parts = line.split("\\|");
        return new StartupIdea(Integer.parseInt(parts[0]), parts[1], parts[2], parts[3], parts[4], parts[5]);
    }

    public void display() {
        System.out.println("ID: " + id);
        System.out.println("Title: " + title);
        System.out.println("Problem: " + problem);
        System.out.println("Solution: " + solution);
        System.out.println("Category: " + category);
        System.out.println("Target Users: " + targetUsers);
        System.out.println("-------------------------");
    }
}

public class StartupIdeaVault {
    static final String FILE_NAME = "ideas.txt";

    public static void main(String[] args) throws IOException {
        Scanner sc = new Scanner(System.in);
        int choice;

        do {
            System.out.println("\n--- Startup Idea Vault ---");
            System.out.println("1. Add New Idea");
            System.out.println("2. View All Ideas");
            System.out.println("3. Search Idea");
            System.out.println("4. Exit");
            System.out.print("Enter your choice: ");
            choice = Integer.parseInt(sc.nextLine());

            switch (choice) {
                case 1:
                    addIdea(sc);
                    break;
                case 2:
                    viewAllIdeas();
                    break;
                case 3:
                    searchIdea(sc);
                    break;
                case 4:
                    System.out.println("Exiting. Stay innovative!");
                    break;
                default:
                    System.out.println("Invalid choice. Try again.");
            }
        } while (choice != 4);
    }

    static void addIdea(Scanner sc) throws IOException {
        int id = getNextId();
        System.out.print("Enter Idea Title: ");
        String title = sc.nextLine();
        System.out.print("Enter Problem: ");
        String problem = sc.nextLine();
        System.out.print("Enter Solution: ");
        String solution = sc.nextLine();
        System.out.print("Enter Category: ");
        String category = sc.nextLine();
        System.out.print("Enter Target Users: ");
        String targetUsers = sc.nextLine();

        StartupIdea idea = new StartupIdea(id, title, problem, solution, category, targetUsers);

        BufferedWriter bw = new BufferedWriter(new FileWriter(FILE_NAME, true));
        bw.write(idea.toFileString());
        bw.newLine();
        bw.close();

        System.out.println("Idea added successfully!");
    }

    static void viewAllIdeas() throws IOException {
        File file = new File(FILE_NAME);
        if (!file.exists() || file.length() == 0) {
            System.out.println("No ideas found.");
            return;
        }

        BufferedReader br = new BufferedReader(new FileReader(FILE_NAME));
        String line;
        while ((line = br.readLine()) != null) {
            StartupIdea idea = StartupIdea.fromFileString(line);
            idea.display();
        }
        br.close();
    }

    static void searchIdea(Scanner sc) throws IOException {
        System.out.print("Enter keyword to search (title/category): ");
        String keyword = sc.nextLine().toLowerCase();

        BufferedReader br = new BufferedReader(new FileReader(FILE_NAME));
        String line;
        boolean found = false;

        while ((line = br.readLine()) != null) {
            StartupIdea idea = StartupIdea.fromFileString(line);
            if (idea.title.toLowerCase().contains(keyword) || idea.category.toLowerCase().contains(keyword)) {
                idea.display();
                found = true;
            }
        }

        if (!found) {
            System.out.println("No matching ideas found.");
        }

        br.close();
    }

    static int getNextId() throws IOException {
        File file = new File(FILE_NAME);
        if (!file.exists()) return 1;

        BufferedReader br = new BufferedReader(new FileReader(FILE_NAME));
        String last = null, line;
        while ((line = br.readLine()) != null) {
            last = line;
        }
        br.close();

        if (last == null) return 1;
        return Integer.parseInt(last.split("\\|")[0]) + 1;
    }
}
